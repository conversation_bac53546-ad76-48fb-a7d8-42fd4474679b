import BaseEventHandler from './BaseEventHandler'
import { getDefaultPreferences } from '../common_base'

/**
 * 用户事件处理器
 * 处理用户相关的事件，如用户信息更新、好友申请等
 */
class UserEventHandler extends BaseEventHandler {
    /**
     * 初始化用户相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("userResponseFriendApply", (data) => {
            this.friendApplyResponse(data)
        })

        controller.on("userAddFriend", (data) => {
            this.notifyAddFriend(data.friendInfo)
        })

        controller.on("userApplyAddFriend", (data) => {
            this.setFriendApplys(data)
        })

        controller.on("notify_add_friend", (data) => {
            this.notifyAddFriend(data)
        })

        controller.on("update_friend_info", (data) => {
            this.updateFriendInfo(data)
        })

        controller.on("notify_friend_destroy", (data) => {
            this.updateFriendDestroy(data)
        })

        controller.on("update_user_info", (data) => {
            this.onUpdateUserInfo(data)
        })

        controller.on("update_user_portrait_img", (data) => {
            this.onUpdateUserPortraitInfo(data)
        })

        controller.on("notify_login_another", () => {
            this.notifyLoginAnother()
        })

        controller.on("notify_user_destroy", () => {
            this.notifyUserDestroy()
        })

        controller.on('user_info', (is_succ, info) => {
            info.preferences = getDefaultPreferences(info)
            this.vm.$store.commit('user/updateUser', info)
            this.vm.checkProfessionalIdentity(info)
        })

        controller.on('version_info', (info) => {
            window.server_info = info
        })
    }

    // 以下方法需要从原EventListenerManager中移动过来
    friendApplyResponse(data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    notifyAddFriend(friend) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setFriendApplys(data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    updateFriendInfo(data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    updateFriendDestroy(data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    onUpdateUserInfo(data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    onUpdateUserPortraitInfo(data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    notifyLoginAnother() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    notifyUserDestroy() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }
}

export default UserEventHandler

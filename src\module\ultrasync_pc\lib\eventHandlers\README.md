# EventListenerManager 拆分方案

## 概述

原始的 `EventListenerManager.js` 文件包含1877行代码，处理了main_screen的所有事件监听逻辑。为了提高代码的可维护性和可读性，我们将其按功能模块拆分成多个独立的事件处理器。

## 拆分结构

### 基类
- `BaseEventHandler.js` - 事件处理器基类，提供通用属性和方法

### 具体事件处理器

1. **GatewayEventHandler.js** - 网关事件处理器
   - 处理网关连接、断开、重连等事件
   - 包含方法：gateway_connect, gateway_error, gateway_reconnecting 等

2. **UserEventHandler.js** - 用户事件处理器
   - 处理用户信息更新、好友申请等事件
   - 包含方法：userResponseFriendApply, userAddFriend, update_user_info 等

3. **ConversationEventHandler.js** - 对话事件处理器
   - 处理会话开始、群消息等事件
   - 包含方法：notify_start_conversation, receive_group_message 等

4. **LiveEventHandler.js** - 直播事件处理器
   - 处理直播开始/停止、录制等事件
   - 包含方法：notify_agora_live_start, notify_agora_live_stop 等

5. **ResourceEventHandler.js** - 资源事件处理器
   - 处理资源删除等事件
   - 包含方法：notify.group.resource.delete.exam 等

6. **GroupsetEventHandler.js** - 群组事件处理器
   - 处理群组管理相关事件
   - 包含方法：notify_delete_group, notify_refresh_manager_groupset_list 等

7. **HomeworkEventHandler.js** - 作业事件处理器
   - 处理作业相关事件
   - 包含方法：student_answer_sheet_update, teacher_answer_sheet_update 等

8. **DeviceEventHandler.js** - 设备事件处理器
   - 处理设备告警等事件
   - 包含方法：equipment_server_device_alram_update 等

9. **NotificationEventHandler.js** - 通知事件处理器
   - 处理异常通知、下载任务等事件
   - 包含方法：notify_exception, notify_download_task 等

10. **MediaTransferEventHandler.js** - 媒体传输事件处理器
    - 处理媒体传输任务相关事件
    - 包含方法：notify_update_media_transfer_task 等

11. **ServerInfoEventHandler.js** - 服务器信息事件处理器
    - 处理服务器配置相关事件
    - 包含方法：server_info 等

### 协调器
- `RefactoredEventListenerManager.js` - 重构后的主管理器，作为各个事件处理器的协调器

## 优势

1. **代码分离** - 每个处理器专注于特定功能领域
2. **易于维护** - 修改某个功能时只需要关注对应的处理器
3. **可测试性** - 每个处理器可以独立进行单元测试
4. **可扩展性** - 新增功能时可以创建新的处理器
5. **代码复用** - 通过基类提供通用功能

## 使用方式

```javascript
import RefactoredEventListenerManager from './eventHandlers/RefactoredEventListenerManager'

// 创建管理器实例
const eventManager = new RefactoredEventListenerManager(vueInstance, autoLoginManager, mainScreenManager)

// 初始化事件监听
eventManager.initMainScreenControllerEvent(controller)
```

## 实施状态

✅ **已完成**：
1. 创建基类和各个事件处理器的框架
2. 实现所有12个事件处理器的基本结构
3. 创建重构后的主管理器
4. 编写使用示例和迁移指南

🔄 **待完善**：
1. 将原文件中的具体方法实现迁移到对应的处理器中
2. 完善各处理器之间的依赖关系
3. 更新所有导入和依赖关系
4. 进行全面测试确保功能正常

## 迁移步骤

1. ✅ 创建基类和各个事件处理器的框架
2. 🔄 逐步将原文件中的方法迁移到对应的处理器中
3. 🔄 更新导入和依赖关系
4. 🔄 测试确保功能正常
5. 🔄 替换原有的EventListenerManager使用

## 注意事项

- 某些方法可能被多个处理器使用，需要考虑放在基类或工具类中
- 保持原有的事件监听逻辑不变，只是重新组织代码结构
- 确保所有依赖的导入都正确更新

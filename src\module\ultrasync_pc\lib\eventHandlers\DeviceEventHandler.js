import BaseEventHandler from './BaseEventHandler'

/**
 * 设备事件处理器
 * 处理设备相关的事件，如设备告警等
 */
class DeviceEventHandler extends BaseEventHandler {
    /**
     * 初始化设备相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on('equipment_server_device_alram_update', (data) => {
            let deviceFailure = this.vm.$store.state.device.deviceFailure
            let num = deviceFailure[data.device_id] || 0
            if (data.status === 'NEW') {
                num++
            } else if (data.status === 'RESOLVE') {
                num > 0 ? num-- : 0
            }
            deviceFailure[data.device_id] = num
            this.vm.$store.commit('device/updateDeviceFailure', deviceFailure)
        })
    }

    /**
     * 根据ID获取设备名称
     */
    getDeviceNameById() {
        if (!this.vm.isCef) {
            return
        }
        if (!this.vm.$store.state.deviceInfo.device_id) {
            setTimeout(() => {
                this.getDeviceNameById();
            }, 3000)
            return;
        }
        return new Promise((resolve, reject) => {
            const params = {
                deviceId: this.vm.$store.state.deviceInfo.device_id,
            }
            window.main_screen.getDeviceNameById(params, (res) => {
                if (res.error_code === 0) {
                    this.vm.$store.commit('device/updateDeviceInfo', { device_name: res.data.name })
                    resolve(true)
                } else {
                    reject(res.error_msg)
                }
            })
        })
    }
}

export default DeviceEventHandler

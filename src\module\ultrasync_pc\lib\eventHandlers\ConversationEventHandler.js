import BaseEventHandler from './BaseEventHandler'
import { getLiveRoomObj, closeChatWindowIfNeed } from '../common_base'

/**
 * 对话事件处理器
 * 处理对话、群组、资源和通知相关的事件
 */
class ConversationEventHandler extends BaseEventHandler {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        super(vueInstance, autoLoginManager, mainScreenManager)
        this.debounceSortChatList = null // 需要从原文件中获取
    }

    /**
     * 初始化对话相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        // 对话相关事件
        this.initConversationEvents(controller)

        // 群组相关事件
        this.initGroupsetEvents(controller)

        // 资源相关事件
        this.initResourceEvents(controller)

        // 通知相关事件
        this.initNotificationEvents(controller)
    }

    /**
     * 初始化对话相关事件
     * @param {Object} controller 控制器
     */
    initConversationEvents(controller) {
        controller.on("notify_start_conversation", (is_succ, conversation, start_type, kickout_data) => {
            this.NotifyStartConversation(is_succ, conversation, start_type, kickout_data)
        })

        controller.on('request_conversation_start_ultrasound_desktop', (data) => {
            this.vm.notifyStartConversationByMonitorWall(data)
        })

        controller.on("receive_group_message", (data) => {
            console.log('receive_group_message', data, 2)
            if (!this.vm.conversationList.hasOwnProperty(data.group_id)) {
                this.setSayChatMessageReceiveGroupMessage(data, false)
                if (data.msg_type === this.vm.systemConfig.msg_type.LIVE_INVITE ||
                    data.groupInfo.service_type === this.vm.systemConfig.ServiceConfig.type.LiveBroadCast) {
                    this.vm.debounceUpdateLiveCount()
                }
                if (this.debounceSortChatList) {
                    this.debounceSortChatList()
                }
            }
        })

        controller.on("recent_active_conversation_list", (is_succ, data) => {
            this.setCurrentList(is_succ, data)
            window.CWorkstationCommunicationMng.QueryStandaloneWorkstationShareExamInfo()
        })

        controller.on("recent_active_conversation_list_last_message", (is_succ, data) => {
            this.setLastMessage(is_succ, data)
            this.vm.$store.commit('chatList/updateUnReadMap', data)
        })

        controller.on("friend_list", (is_succ, data) => {
            this.setFriendList(is_succ, data)
            setTimeout(() => {
                this.autoUploadDrAiData()
            }, 2500)
        })

        controller.on("userAddLoginClient", (data) => {
            if (data.allClientType.length > 1) {
                this.isTopFileTransferAssistant = true
                if (this.hasSetCurrentList) {
                    this.topFileTransferAssistant()
                }
            }
        })

        controller.on("conversation_list", (is_succ, data) => {
            this.setGroupList(is_succ, data)
        })

        controller.on("group_applys", (is_succ, data) => {
            this.setGroupApplys(is_succ, data)
        })

        controller.on("friend_applys", (is_succ, data) => {
            this.dealFriendApplys(is_succ, data)
        })
    }

    /**
     * 初始化群组相关事件
     * @param {Object} controller 控制器
     */
    initGroupsetEvents(controller) {
        controller.on("notify_delete_group", (json_str) => {
            let data = typeof json_str === 'string' ? JSON.parse(json_str) : json_str
            this.vm.$store.commit('chatList/deleteChatList', { cid: data.cid })
            this.vm.$store.commit('groupList/deleteGroupList', { cid: data.cid })
            this.vm.$store.commit('conversationList/deleteConversationList', { cid: data.cid })
            this.vm.$store.commit('notifications/deleteGroupApplyByCid', { cid: data.cid })
            this.vm.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', { cid: data.cid })

            window.main_screen.conversation_list[data.cid].onResponseDeleteAttendee()
            let liveRoom = getLiveRoomObj(this.vm.$root.currentLiveCid)
            if (liveRoom) {
                liveRoom.LeaveChannelAux()
                window.CWorkstationCommunicationMng.StopConference()
            }
            closeChatWindowIfNeed(data.cid)
            setTimeout(() => {
                this.vm.$message.success(this.vm.lang.user_exit_group_succ)
            }, 100)
        })

        controller.on("open_register_scan_room_view", (data) => {
            const attributes = {
                mac_addr: data.mac_addr,
                name: "",
                hospital_id: 0,
                ultrasync_box_mac_addr: data.mac_addr
            }
            this.vm.$store.commit('user/updateUser', {
                scan_room: attributes
            })
        })

        controller.on('notify_refresh_manager_groupset_list', (data) => {
            console.log('notify_refresh_manager_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getManagerGroupsetList()
        })

        controller.on('notify_refresh_my_groupset_list', (data) => {
            console.log('notify_refresh_my_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getGroupSetList()
        })
    }

    /**
     * 初始化资源相关事件
     * @param {Object} controller 控制器
     */
    initResourceEvents(controller) {
        controller.on("notify.group.resource.delete.exam", (data) => {
            console.log(data, 'notify.group.resource.delete.exam')
            this.vm.$store.commit('examList/deleteExamListItem', {
                cid: data.groupID,
                exam_id: data.examID,
            })
            this.vm.$root.eventBus.$emit('deleteExamItem')

            if (Array.isArray(data.deleteResourceIDList)) {
                data.deleteResourceIDList.forEach(resource_id => {
                    this.vm.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id,
                        data: {
                            state: 0 // 被删除
                        }
                    })
                    this.vm.$store.commit('chatList/updateLastChatMessageByResourceDelete', {
                        cid: data.groupID,
                        data: {
                            msg_type: this.vm.systemConfig.msg_type.ResourceDelete,
                            resource_id
                        }
                    })
                })
            }

            if (Array.isArray(data.deleteMessageIDList)) {
                this.vm.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                    gmsg_id_list: data.deleteMessageIDList,
                    cid: data.groupID
                })
                this.vm.$root.eventBus.$emit('notifyDeleteChatMessages', {
                    cid: data.groupID,
                    gmsg_id_list: data.deleteMessageIDList
                })
            }
        })

        controller.on('notify.group.resource.delete.resource', (data) => {
            if (Array.isArray(data.deleteResourceIDList)) {
                data.deleteResourceIDList.forEach(resource_id => {
                    this.vm.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                        resource_id,
                        data: {
                            state: 0 // 被删除
                        }
                    })
                    if (data.deleteMessageIDList.length > 0) {
                        this.vm.$store.commit('chatList/updateLastChatMessageByResourceDelete', {
                            cid: data.groupID,
                            data: {
                                msg_type: this.vm.systemConfig.msg_type.ResourceDelete,
                                resource_id
                            }
                        })
                    }
                    this.vm.$root.eventBus.$emit('deleteFileToExamList', {
                        cid: data.groupID,
                        resource_id
                    })
                })
            }

            if (Array.isArray(data.deleteMessageIDList)) {
                this.vm.$store.commit("conversationList/deleteChatMessagesByGmsgIdList", {
                    gmsg_id_list: data.deleteMessageIDList,
                    cid: data.groupID
                })
                this.vm.$root.eventBus.$emit('notifyDeleteChatMessages', {
                    cid: data.groupID,
                    gmsg_id_list: data.deleteMessageIDList
                })
            }
        })
    }

    /**
     * 初始化通知相关事件
     * @param {Object} controller 控制器
     */
    initNotificationEvents(controller) {
        controller.on("notify_exception", () => {
            this.autoLoginManager.onNotifyException()
        })

        controller.on("NotifyStandaloneWorkstationShareExamInfo", (data) => {
            this.NotifyStandaloneWorkstationShareExamInfo(data)
        })

        controller.on("notify_download_task", (data) => {
            if ("error" === data.type) {
                this.vm.$message.error(data.errorInfo)
            }
        })

        controller.on("notify_update_groupset_portrait", (err, result) => {
            if (!err) {
                this.notifyUpdateGroupsetAvatar(result)
            }
        })
    }

    // 以下方法需要从原EventListenerManager中移动过来
    NotifyStartConversation(is_succ, conversation, start_type, kickout_data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setSayChatMessageReceiveGroupMessage(data, is_open_conversation) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setCurrentList(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setLastMessage(is_succ, list) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setFriendList(is_succ, list) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    autoUploadDrAiData() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    topFileTransferAssistant() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setGroupList(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setGroupApplys(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    dealFriendApplys(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    /**
     * 获取管理员群组列表
     */
    getManagerGroupsetList() {
        window.main_screen.getManagerGroupsetList({}, (data) => {
            console.log(data, 'getManagerGroupsetList')
            if (data.error_code == 0) {
                let list = data.data;
                list.forEach(item => {
                    item.type = this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetManagerList', data.data);
            }
        })
    }

    /**
     * 获取群组列表
     */
    getGroupSetList() {
        window.main_screen.getGroupsetList({}, (data) => {
            console.log('getGroupsetList', data)
            if (data.error_code == 0) {
                let list = data.data;
                list.forEach(item => {
                    item.type = this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetList', data.data);
            }
        })
        this.getManagerGroupsetList()
    }

    /**
     * 通知独立工作站共享检查信息
     * @param {Object} data 数据
     */
    NotifyStandaloneWorkstationShareExamInfo(data) {
        // 判断是否在电视墙下，发起实时下，阅片下
        if (window.vm.$store.state.dynamicGlobalParams.tv_wall_mode) { // 在电视墙下禁止操作
            window.CWorkstationCommunicationMng.ShowConfirmDialog({
                title: this.vm.lang.warning_title,
                message: this.vm.lang.tv_wall_warning_message,
                yes_button: this.vm.lang.confirm_txt,
            });
            window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
            return;
        }

        if (this.vm.$route.name == 'gallery') { // 在阅片下，转发窗口盖在最上层
            console.log("gallery");
            this.vm.$root.eventBus.$emit('hideRealTimeVideo');
        }

        console.log("NotifyStandaloneWorkstationShareExamInfo ", data);

        this.vm.$root.eventBus.$emit('openTransmit', {
            callback: this.generalTransmitSubmit,
            isStandaloneWorkstationShare: 1,
            cid: this.vm.$route.params.cid,
        })
    }

    /**
     * 通知更新群组头像
     * @param {Object} result 结果数据
     */
    notifyUpdateGroupsetAvatar(result) {
        let groupset = {
            avatar: result.avatar
        }
        this.vm.$store.commit('groupset/updateGroupsetAvatar', {
            avatar: groupset.avatar,
            id: result.groupset_id,
        })
        this.vm.$message.success(this.vm.lang.modify_photo_success);
        this.vm.back();
    }

    /**
     * 通用转发提交
     * @param {Object} data 数据
     */
    generalTransmitSubmit(data) {
        console.log("generalTransmitSubmit ", data);
        if (this.vm.$route.name == 'gallery') {
            this.vm.$root.eventBus.$emit('showRealTimeVideo');
        }

        Object.entries(this.vm.$store.state.conversationList).map(item => { // 判断会话列表中是否有与fid对应的会话 有则直接取之前的cid
            if (data.id && item[1].fid === data.id) {
                data.cid = item[0]
            }
        })
        this.vm.$root.transmitQueue_StandaloneWorkstation[data.cid || 'f-' + data.id] = 1;
        if (this.vm.$store.state.conversationList[data.cid]) {
            // 会话已开启则直接转发
            window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(data.cid);
        } else {
            // 会话未开启则开启会话
            if (data.cid) {
                this.vm.openConversation(data.cid, 7)
            } else {
                this.vm.openConversation(data.id, 3)
            }
        }
    }
}

export default ConversationEventHandler

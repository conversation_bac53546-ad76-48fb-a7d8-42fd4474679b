import BaseEventHandler from './BaseEventHandler'

/**
 * 对话事件处理器
 * 处理对话相关的事件，如会话开始、群消息等
 */
class ConversationEventHandler extends BaseEventHandler {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        super(vueInstance, autoLoginManager, mainScreenManager)
        this.debounceSortChatList = null // 需要从原文件中获取
    }

    /**
     * 初始化对话相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("notify_start_conversation", (is_succ, conversation, start_type, kickout_data) => {
            this.NotifyStartConversation(is_succ, conversation, start_type, kickout_data)
        })

        controller.on('request_conversation_start_ultrasound_desktop', (data) => {
            this.vm.notifyStartConversationByMonitorWall(data)
        })

        controller.on("receive_group_message", (data) => {
            console.log('receive_group_message', data, 2)
            if (!this.vm.conversationList.hasOwnProperty(data.group_id)) {
                this.setSayChatMessageReceiveGroupMessage(data, false)
                if (data.msg_type === this.vm.systemConfig.msg_type.LIVE_INVITE ||
                    data.groupInfo.service_type === this.vm.systemConfig.ServiceConfig.type.LiveBroadCast) {
                    this.vm.debounceUpdateLiveCount()
                }
                if (this.debounceSortChatList) {
                    this.debounceSortChatList()
                }
            }
        })

        controller.on("recent_active_conversation_list", (is_succ, data) => {
            this.setCurrentList(is_succ, data)
            window.CWorkstationCommunicationMng.QueryStandaloneWorkstationShareExamInfo()
        })

        controller.on("recent_active_conversation_list_last_message", (is_succ, data) => {
            this.setLastMessage(is_succ, data)
            this.vm.$store.commit('chatList/updateUnReadMap', data)
        })

        controller.on("friend_list", (is_succ, data) => {
            this.setFriendList(is_succ, data)
            setTimeout(() => {
                this.autoUploadDrAiData()
            }, 2500)
        })

        controller.on("userAddLoginClient", (data) => {
            if (data.allClientType.length > 1) {
                this.isTopFileTransferAssistant = true
                if (this.hasSetCurrentList) {
                    this.topFileTransferAssistant()
                }
            }
        })

        controller.on("conversation_list", (is_succ, data) => {
            this.setGroupList(is_succ, data)
        })

        controller.on("group_applys", (is_succ, data) => {
            this.setGroupApplys(is_succ, data)
        })

        controller.on("friend_applys", (is_succ, data) => {
            this.dealFriendApplys(is_succ, data)
        })
    }

    // 以下方法需要从原EventListenerManager中移动过来
    NotifyStartConversation(is_succ, conversation, start_type, kickout_data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setSayChatMessageReceiveGroupMessage(data, is_open_conversation) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setCurrentList(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setLastMessage(is_succ, list) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setFriendList(is_succ, list) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    autoUploadDrAiData() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    topFileTransferAssistant() {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setGroupList(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    setGroupApplys(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    dealFriendApplys(is_succ, data) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }
}

export default ConversationEventHandler

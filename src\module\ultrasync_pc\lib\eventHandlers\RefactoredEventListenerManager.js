/**
 * 重构后的事件监听管理器
 * 作为各个事件处理器的协调器，负责初始化和管理各个子处理器
 */
import GatewayEventHandler from './GatewayEventHandler'
import UserEventHandler from './UserEventHandler'
import ConversationEventHandler from './ConversationEventHandler'
import LiveEventHandler from './LiveEventHandler'
import ResourceEventHandler from './ResourceEventHandler'
import GroupsetEventHandler from './GroupsetEventHandler'
import HomeworkEventHandler from './HomeworkEventHandler'
import DeviceEventHandler from './DeviceEventHandler'
import NotificationEventHandler from './NotificationEventHandler'
import MediaTransferEventHandler from './MediaTransferEventHandler'
import ServerInfoEventHandler from './ServerInfoEventHandler'

import Tool from '@/common/tool.js'

class RefactoredEventListenerManager {
    constructor(vueInstance, autoLoginManager, mainScreenManager) {
        this.vm = vueInstance
        this.autoLoginManager = autoLoginManager
        this.mainScreenManager = mainScreenManager

        // 初始化各个事件处理器
        this.gatewayHandler = new GatewayEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.userHandler = new UserEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.conversationHandler = new ConversationEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.liveHandler = new LiveEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.resourceHandler = new ResourceEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.groupsetHandler = new GroupsetEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.homeworkHandler = new HomeworkEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.deviceHandler = new DeviceEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.notificationHandler = new NotificationEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.mediaTransferHandler = new MediaTransferEventHandler(vueInstance, autoLoginManager, mainScreenManager)
        this.serverInfoHandler = new ServerInfoEventHandler(vueInstance, autoLoginManager, mainScreenManager)

        // 保留一些共享的属性
        this.isFirstLoadServerInfo = false
        this.hasSetCurrentList = false
        this.isTopFileTransferAssistant = false
        this.debounceSortChatList = Tool.debounce(this.sortChatList, 600, true)

        // 将共享的方法和属性传递给需要的处理器
        this.conversationHandler.debounceSortChatList = this.debounceSortChatList
        this.liveHandler.debounceSortChatList = this.debounceSortChatList
        this.serverInfoHandler.isFirstLoadServerInfo = this.isFirstLoadServerInfo

        // 将作业相关方法传递给需要的处理器
        this.homeworkHandler.getCorrectedHomework = this.getCorrectedHomework.bind(this)
        this.homeworkHandler.getUncorrectHomework = this.getUncorrectHomework.bind(this)
        this.homeworkHandler.getUnfinishedHomework = this.getUnfinishedHomework.bind(this)
    }

    /**
     * 初始化所有事件监听器
     * @param {Object} controller MainScreen控制器
     */
    initMainScreenControllerEvent(controller) {
        console.log('[RefactoredEventListenerManager] init controller events')

        this.autoLoginManager.isAutoLogging = false

        // 初始化各类事件监听器
        this.gatewayHandler.initEvents(controller)
        this.userHandler.initEvents(controller)
        this.conversationHandler.initEvents(controller)
        this.liveHandler.initEvents(controller)
        this.resourceHandler.initEvents(controller)
        this.groupsetHandler.initEvents(controller)
        this.homeworkHandler.initEvents(controller)
        this.deviceHandler.initEvents(controller)
        this.notificationHandler.initEvents(controller)
        this.mediaTransferHandler.initEvents(controller)
        this.serverInfoHandler.initEvents(controller)

        // 初始化作业相关数据
        this._initHomeworkData()

        // 更新全局参数
        this.vm.$store.commit('globalParams/updateGlobalParams', {
            init_main_screen_time: new Date().getTime()
        })
    }

    /**
     * 初始化作业相关数据
     */
    _initHomeworkData() {
        this.getUnfinishedHomework(0)
        this.getUncorrectHomework(0)
        this.getCorrectedHomework(0)
    }

    /**
     * 排序聊天列表
     */
    sortChatList() {
        this.vm.$store.commit('chatList/sortChatList')
    }

    // 以下方法需要从原EventListenerManager中移动过来或重新实现
    getUnfinishedHomework(cid) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    getUncorrectHomework(cid) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }

    getCorrectedHomework(cid) {
        // 这个方法需要从原文件中移动过来
        // 暂时保留空实现，后续会完善
    }
}

export default RefactoredEventListenerManager

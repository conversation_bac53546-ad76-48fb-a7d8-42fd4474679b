import BaseEventHandler from './BaseEventHandler'

/**
 * 通知事件处理器
 * 处理通知相关的事件，如异常通知、下载任务等
 */
class NotificationEventHandler extends BaseEventHandler {
    /**
     * 初始化通知相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("notify_exception", () => {
            this.autoLoginManager.onNotifyException()
        })

        controller.on("NotifyStandaloneWorkstationShareExamInfo", (data) => {
            this.NotifyStandaloneWorkstationShareExamInfo(data)
        })

        controller.on("notify_download_task", (data) => {
            if ("error" === data.type) {
                this.vm.$message.error(data.errorInfo)
            }
        })

        controller.on("notify_update_groupset_portrait", (err, result) => {
            if (!err) {
                this.notifyUpdateGroupsetAvatar(result)
            }
        })
    }

    /**
     * 通知独立工作站共享检查信息
     * @param {Object} data 数据
     */
    NotifyStandaloneWorkstationShareExamInfo(data) {
        // 判断是否在电视墙下，发起实时下，阅片下
        if (window.vm.$store.state.dynamicGlobalParams.tv_wall_mode) { // 在电视墙下禁止操作
            window.CWorkstationCommunicationMng.ShowConfirmDialog({
                title: this.vm.lang.warning_title,
                message: this.vm.lang.tv_wall_warning_message,
                yes_button: this.vm.lang.confirm_txt,
            });
            window.CWorkstationCommunicationMng.ClearStandaloneWorkstationShareExamInfo();
            return;
        }

        if (this.vm.$route.name == 'gallery') { // 在阅片下，转发窗口盖在最上层
            console.log("gallery");
            this.vm.$root.eventBus.$emit('hideRealTimeVideo');
        }

        console.log("NotifyStandaloneWorkstationShareExamInfo ", data);

        this.vm.$root.eventBus.$emit('openTransmit', {
            callback: this.generalTransmitSubmit,
            isStandaloneWorkstationShare: 1,
            cid: this.vm.$route.params.cid,
        })
    }

    /**
     * 通知更新群组头像
     * @param {Object} result 结果数据
     */
    notifyUpdateGroupsetAvatar(result) {
        let groupset = {
            avatar: result.avatar
        }
        this.vm.$store.commit('groupset/updateGroupsetAvatar', {
            avatar: groupset.avatar,
            id: result.groupset_id,
        })
        this.vm.$message.success(this.vm.lang.modify_photo_success);
        this.vm.back();
    }

    /**
     * 通用转发提交
     * @param {Object} data 数据
     */
    generalTransmitSubmit(data) {
        console.log("generalTransmitSubmit ", data);
        if (this.vm.$route.name == 'gallery') {
            this.vm.$root.eventBus.$emit('showRealTimeVideo');
        }

        Object.entries(this.vm.$store.state.conversationList).map(item => { // 判断会话列表中是否有与fid对应的会话 有则直接取之前的cid
            if (data.id && item[1].fid === data.id) {
                data.cid = item[0]
            }
        })
        this.vm.$root.transmitQueue_StandaloneWorkstation[data.cid || 'f-' + data.id] = 1;
        if (this.vm.$store.state.conversationList[data.cid]) {
            // 会话已开启则直接转发
            window.CWorkstationCommunicationMng.SendStandaloneWorkstationShareExamInfo(data.cid);
        } else {
            // 会话未开启则开启会话
            if (data.cid) {
                this.vm.openConversation(data.cid, 7)
            } else {
                this.vm.openConversation(data.id, 3)
            }
        }
    }
}

export default NotificationEventHandler

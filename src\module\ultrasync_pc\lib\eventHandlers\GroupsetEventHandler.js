import BaseEventHandler from './BaseEventHandler'
import { getLiveRoomObj, closeChatWindowIfNeed } from '../common_base'

/**
 * 群组事件处理器
 * 处理群组相关的事件，如群组管理等
 */
class GroupsetEventHandler extends BaseEventHandler {
    /**
     * 初始化群组相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("notify_delete_group", (json_str) => {
            let data = typeof json_str === 'string' ? JSON.parse(json_str) : json_str
            this.vm.$store.commit('chatList/deleteChatList', { cid: data.cid })
            this.vm.$store.commit('groupList/deleteGroupList', { cid: data.cid })
            this.vm.$store.commit('conversationList/deleteConversationList', { cid: data.cid })
            this.vm.$store.commit('notifications/deleteGroupApplyByCid', { cid: data.cid })
            this.vm.$store.commit('consultationImageList/deleteConsultationImageListByGroupID', { cid: data.cid })

            window.main_screen.conversation_list[data.cid].onResponseDeleteAttendee()
            let liveRoom = getLiveRoomObj(this.vm.$root.currentLiveCid)
            if (liveRoom) {
                liveRoom.LeaveChannelAux()
                window.CWorkstationCommunicationMng.StopConference()
            }
            closeChatWindowIfNeed(data.cid)
            setTimeout(() => {
                this.vm.$message.success(this.vm.lang.user_exit_group_succ)
            }, 100)
        })

        controller.on("open_register_scan_room_view", (data) => {
            const attributes = {
                mac_addr: data.mac_addr,
                name: "",
                hospital_id: 0,
                ultrasync_box_mac_addr: data.mac_addr
            }
            this.vm.$store.commit('user/updateUser', {
                scan_room: attributes
            })
        })

        controller.on('notify_refresh_manager_groupset_list', (data) => {
            console.log('notify_refresh_manager_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getManagerGroupsetList()
        })

        controller.on('notify_refresh_my_groupset_list', (data) => {
            console.log('notify_refresh_my_groupset_list', data)
            if (data.action === 'delete' && (Number(this.vm.$route.params.groupset_id) === data.groupSetID)) {
                this.vm.$router.replace('/main/index/chat_window/0')
            }
            this.getGroupSetList()
        })
    }

    /**
     * 获取管理员群组列表
     */
    getManagerGroupsetList() {
        window.main_screen.getManagerGroupsetList({}, (data) => {
            console.log(data, 'getManagerGroupsetList')
            if (data.error_code == 0) {
                let list = data.data;
                list.forEach(item => {
                    item.type = this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetManagerList', data.data);
            }
        })
    }

    /**
     * 获取群组列表
     */
    getGroupSetList() {
        window.main_screen.getGroupsetList({}, (data) => {
            console.log('getGroupsetList', data)
            if (data.error_code == 0) {
                let list = data.data;
                list.forEach(item => {
                    item.type = this.vm.systemConfig.ConversationConfig.type.GroupSet;
                })
                this.vm.$store.commit('groupset/initGroupsetList', data.data);
            }
        })
        this.getManagerGroupsetList()
    }
}

export default GroupsetEventHandler

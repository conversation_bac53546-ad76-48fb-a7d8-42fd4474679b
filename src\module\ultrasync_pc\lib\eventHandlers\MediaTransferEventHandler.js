import BaseEventHandler from './BaseEventHandler'

/**
 * 媒体传输事件处理器
 * 处理媒体传输相关的事件
 */
class MediaTransferEventHandler extends BaseEventHandler {
    /**
     * 初始化媒体传输相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("notify_update_media_transfer_task", (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/updateMediaTransferTasks', result.list)
            }
        })

        controller.on("notify_delete_media_transfer_task", (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/deleteMediaTransferTasks', result.list)
            }
        })

        // 查询媒体传输任务
        controller.emit("query_media_transfer_tasks", {}, (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/initMediaTransferTasks', result.list)
            }
        })
    }
}

export default MediaTransferEventHandler
